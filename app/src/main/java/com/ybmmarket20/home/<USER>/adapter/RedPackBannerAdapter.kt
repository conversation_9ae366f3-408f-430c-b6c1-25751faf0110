package com.ybmmarket20.home.mine.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.databinding.ItemRedPackBannerBinding
import com.ybmmarket20.home.mine.bean.RedPackBannerItem
import com.youth.banner.adapter.BannerAdapter

/**
 * 红包轮播适配器
 */
class RedPackBannerAdapter(val mDataList: MutableList<RedPackBannerItem>) :
    BannerAdapter<RedPackBannerItem, RedPackBannerAdapter.RedPackBannerVH>(mDataList) {

    companion object {
        const val TYPE_SHOPPING_GOLD = 1
        const val TYPE_WECHAT_BIND = 2
    }

    override fun onCreateHolder(parent: ViewGroup, viewType: Int): RedPackBannerVH {
        return RedPackBannerVH(
            ItemRedPackBannerBinding.inflate(
                LayoutInflater.from(parent.context), 
                parent, 
                false
            )
        )
    }

    override fun onBindView(
        holder: RedPackBannerVH,
        data: RedPackBannerItem,
        position: Int,
        size: Int
    ) {
        holder.binding.apply {
            ivRedPack.setImageResource(data.iconRes)
            tvTips.text = data.tipsText
            tvGoTopUp.text = data.buttonText
            
            root.setOnClickListener {
                data.clickListener?.invoke()
            }
        }
    }

    class RedPackBannerVH(val binding: ItemRedPackBannerBinding) : RecyclerView.ViewHolder(binding.root)
}
