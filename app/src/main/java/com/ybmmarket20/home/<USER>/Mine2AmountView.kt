package com.ybmmarket20.home.mine

import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.os.Build
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.ShoppingGoldRechargeBean
import com.ybmmarket20.home.mine.bean.Mine2AmountBean
import com.ybmmarket20.home.mine.bean.Mine2HeaderItemContainer
import com.ybmmarket20.home.mine.bean.RedPackBannerItem
import com.ybmmarket20.home.mine.adapter.RedPackBannerAdapter
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.xyyreport.page.mine.MineReport
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import kotlinx.android.synthetic.main.view_mine2_amount.view.*

/**
 * 我的页面-资产
 */
//购物金
const val MINE2_AMOUNT_VIRTUAL_MONEY = 0
//红包
const val MINE2_AMOUNT_RED_ENVELOPE = 1
//优惠券
const val MINE2_AMOUNT_COUPON = 2
//平安贷
const val MINE2_AMOUNT_PING_AN = 3
//我的财富
const val MINE2_AMOUNT_MY_RICH = 4
//资质管理
const val MINE2_AMOUNT_APTITUDE_MANAGE = 5

const val MINE2_AMOUNT_ITEM_TYPE_WITH_NEW = 2

class Mine2AmountView(context: Context, val attr: AttributeSet?) :
    AbsMine2BaseView<MutableList<Mine2AmountBean>>(context, attr) {

    var isShowDivider = true

    private var pingAnClickBlock: (()->Unit)? = null

    override fun getLayoutId(): Int = R.layout.view_mine2_amount

    override fun initialize() {
        super.initialize()
        if (isShowDivider) {
            val divider = Mine2AmountDivider(context)
            rv_mine2_amount.addItemDecoration(divider)
        }

        // 初始化红包轮播Banner
        initRedPackBanner()
    }

    override fun setData(container: Mine2HeaderItemContainer<MutableList<Mine2AmountBean>>) {
        if (!container.entry.isNullOrEmpty()) {
            rv_mine2_amount.layoutManager =
                GridLayoutManager(context, container.entry!!.size, GridLayoutManager.VERTICAL, false)
            rv_mine2_amount.adapter = Mine2AmountAdapter(container.entry!!)
        }
    }

    fun setShoppingGoldData(mBean: ShoppingGoldRechargeBean?){
        // 直接调用红包容器设置逻辑
        setupRedPackContainer(mBean)
    }

    inner class Mine2AmountAdapter(val list: List<Mine2AmountBean>):
        YBMBaseMultiItemAdapter<Mine2AmountBean>(list){

        init {
            addItemType(MINE2_AMOUNT_ITEM_TYPE_WITH_NEW, R.layout.item_mine2_amount_new)
        }

        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: Mine2AmountBean?) {
            whenAllNotNull(baseViewHolder, t) { holder, bean ->
                if (bean.itemType == MINE2_AMOUNT_ITEM_TYPE_WITH_NEW) {
                    bindNewView(holder, bean)
                }
                holder.itemView.setOnClickListener(Mine2AmountItemClickListener(bean.amountType))
            }
        }

        //静态图片
        private fun bindNewView(holder: YBMBaseHolder, bean: Mine2AmountBean) {
            val des = holder.getView<TextView>(R.id.tv_mine2_des)
            val title = holder.getView<TextView>(R.id.tv_mine2_amount)
            title.text = bean.des
            des.text = bean.amount
            // 设置字体大小
            des.setTextSize(android.util.TypedValue.COMPLEX_UNIT_DIP, if (bean.amountType == MINE2_AMOUNT_MY_RICH) 14f else 20f)
            if (bean.amountType == MINE2_AMOUNT_VIRTUAL_MONEY) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    des.setAutoSizeTextTypeUniformWithConfiguration(10, 20, 2, TypedValue.COMPLEX_UNIT_SP)
                }
            }
            holder.setVisible(R.id.v_mine2_amount_divider, isShowDivider)
        }
    }

    inner class Mine2AmountItemClickListener(private val amountType: Int): View.OnClickListener {
        override fun onClick(v: View?) {
            when(amountType) {
                //购物金
                MINE2_AMOUNT_VIRTUAL_MONEY -> {
                    RoutersUtils.open("ybmpage://myvirtualmoney")
                    XyyIoUtil.track("action_virtualGold_Click", hashMapOf("text" to "购物金"))
                    MineReport.clickSubModuleMyWealthShoppingGold(context)
                }
                //红包
                MINE2_AMOUNT_RED_ENVELOPE -> {
                    val redEnvelopeUrl = "ybmpage://myredenvelope"
                    RoutersUtils.open(redEnvelopeUrl)
                    XyyIoUtil.track("action_Me_RedPacket", hashMapOf(
                        "name" to "红包",
                        "action" to redEnvelopeUrl
                    ))
                    MineFragment2.jgTrackBtnClick(context,"上服务区","红包")
                    MineReport.clickSubModuleMyWealthRedEnvelope(context)
                }
                //优惠券
                MINE2_AMOUNT_COUPON -> {
                    val couponUrl = "ybmpage://couponmeber"
                    RoutersUtils.open("ybmpage://couponmeber")
                    XyyIoUtil.track("action_Me_Coupons", hashMapOf(
                        "name" to "优惠券",
                        "action" to couponUrl
                    ))
                    MineFragment2.jgTrackBtnClick(context,"上服务区","优惠券")
                    MineReport.clickSubModuleMyWealthCoupon(context)
                }
                //平安金融
                MINE2_AMOUNT_PING_AN -> {
                    pingAnClickBlock?.invoke()
                }
                //资质管理
                MINE2_AMOUNT_APTITUDE_MANAGE -> {
                    RoutersUtils.open("ybmpage://aptitude")
                    MineFragment2.jgTrackBtnClick(context,"上服务区","资质管理")
                }
                //我的财富
                MINE2_AMOUNT_MY_RICH -> {
                    RoutersUtils.open("ybmpage://mywealth")
                    XyyIoUtil.track("my_wealth_click")
                    MineFragment2.jgTrackBtnClick(context,"上服务区","我的财富")
                    MineReport.clickSubModuleMyWealthMyWealth(context)
                }
            }
        }

    }

    fun setOnPingAnClickListener(block: (()->Unit)?) {
        pingAnClickBlock = block
    }

    inner class Mine2AmountDivider(val context: Context?): RecyclerView.ItemDecoration() {

        val dp = ScreenUtils.dip2px(context, 1f)

        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            val childLayoutPosition = parent.getChildLayoutPosition(view)
            val itemCount = parent.adapter?.itemCount?: 0
            if (childLayoutPosition != itemCount - 1) {
                outRect.set(0, 0, 1 * dp, 0)
            }
        }
    }

    /**
     * 初始化红包轮播Banner
     */
    private fun initRedPackBanner() {
        banner_red_pack_container.apply {
            setLoopTime(3000L) // 3秒轮播间隔
            isAutoLoop(true)
            setIndicator(null) // 不显示指示器
        }
    }

    /**
     * 设置红包容器的显示逻辑
     */
    private fun setupRedPackContainer(mBean: ShoppingGoldRechargeBean?) {
        val bannerItems = mutableListOf<RedPackBannerItem>()

        // 添加购物金红包（如果有数据）
        if (mBean != null) {
            val upperHalf = mBean.highLevelRedPacketMsgUpperHalf ?: ""
            val lowerHalf = mBean.highLevelRedPacketMsgLowerHalf ?: ""
            val fullText = upperHalf + lowerHalf

            if (fullText.isNotEmpty()) {
                bannerItems.add(
                    RedPackBannerItem(
                        type = RedPackBannerAdapter.TYPE_SHOPPING_GOLD,
                        iconRes = R.drawable.icon_red_packet,
                        tipsText = fullText,
                        buttonText = "去充值",
                        clickListener = {
                            RoutersUtils.open("ybmpage://myvirtualmoney?showRechargeDialog=1")
                            MineReport.clickSubModuleMyWealthGoRecharge(context)
                        }
                    )
                )
            }
        }

        // 添加微信红包（如果需要显示）
        if (shouldShowWechatRedPack()) {
            bannerItems.add(
                RedPackBannerItem(
                    type = RedPackBannerAdapter.TYPE_WECHAT_BIND,
                    iconRes = R.drawable.icon_red_packet,
                    tipsText = "首次绑定微信最高得20元红包",
                    buttonText = "去绑定",
                    clickListener = {
                        // 微信绑定逻辑
                    }
                )
            )
        }

        // 根据是否有数据决定显示
        if (bannerItems.isNotEmpty()) {
            // 有数据，显示Banner和提示箭头
            iv_bg_tip.isVisible = true
            banner_red_pack_container.isVisible = true
//
            // 设置Banner数据
            val adapter = RedPackBannerAdapter(bannerItems)
            banner_red_pack_container.setAdapter(adapter, true)
        } else {
            // 没有数据，隐藏所有
            iv_bg_tip.isVisible = false
            banner_red_pack_container.isVisible = false
        }
    }

    /**
     * 检查是否应该显示微信红包
     * 这里可以根据实际业务逻辑判断，比如用户是否已绑定微信等
     */
    private fun shouldShowWechatRedPack(): Boolean {
        // 示例：这里可以添加更复杂的业务逻辑判断
        // 比如检查用户登录状态、微信绑定状态等
        return true // 暂时返回true用于测试
    }
}