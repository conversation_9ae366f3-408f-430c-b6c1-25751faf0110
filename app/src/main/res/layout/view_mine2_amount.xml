<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="wrap_content"
    android:layout_width="match_parent"
    android:paddingBottom="10dp"
    android:background="@drawable/shape_mine2_amount"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_mine2_amount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="15dp"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:itemCount="4"
        tools:listitem="@layout/item_mine2_amount_new" />

    <ImageView
        android:id="@+id/iv_bg_tip"
        android:layout_width="13dp"
        android:src="@drawable/icon_shopping_gold_tips_up"
        app:layout_constraintTop_toBottomOf="@id/rv_mine2_amount"
        android:layout_marginTop="4dp"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="35dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_height="7dp"/>

    <com.youth.banner.Banner
        android:id="@+id/banner_red_pack_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="0dp"
        app:banner_indicator_gravity="center"
        app:banner_indicator_marginBottom="10dp"
        app:banner_indicator_normal_width="5dp"
        app:banner_indicator_selected_width="5dp"
        app:banner_indicator_space="4dp"
        app:banner_round_bottom_left="true"
        app:banner_round_bottom_right="true"
        app:layout_constraintDimensionRatio="354:518"
        app:banner_radius="10dp"
        app:banner_round_top_left="true"
        app:banner_round_top_right="true"
        app:banner_indicator_selected_color="@color/white"
        app:banner_indicator_normal_color="@color/color_80ffffff"
        android:layout_height="0dp"/>
<!--    &lt;!&ndash; 红包轮播Banner &ndash;&gt;-->
<!--    <com.youth.banner.Banner-->
<!--        android:id="@+id/banner_red_pack_container"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="36dp"-->
<!--        android:layout_marginStart="10dp"-->
<!--        android:layout_marginTop="10dp"-->
<!--        android:layout_marginEnd="10dp"-->
<!--        android:visibility="gone"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@id/rv_mine2_amount"-->
<!--        app:banner_radius="4dp"-->
<!--        tools:visibility="visible" />-->

</androidx.constraintlayout.widget.ConstraintLayout>