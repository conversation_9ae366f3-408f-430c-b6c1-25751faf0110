<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingBottom="13dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    
    <ImageView
        android:id="@+id/iv_goods"
        android:layout_width="78dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginHorizontal="10dp"
        android:layout_marginTop="13dp"
        android:layout_height="78dp"/>

    <TextView
        android:id="@+id/tv_specification"
        android:layout_width="0dp"
        app:layout_constraintEnd_toEndOf="@id/iv_goods"
        app:layout_constraintStart_toStartOf="@id/iv_goods"
        app:layout_constraintTop_toBottomOf="@id/iv_goods"
        tools:text="100g/袋"
        android:textSize="12dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="@color/color_292933"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/tv_price"
        app:layout_constraintStart_toStartOf="@id/tv_specification"
        app:layout_constraintTop_toBottomOf="@id/tv_specification"
        android:layout_width="wrap_content"
        tools:text="￥16.70"
        android:textColor="@color/color_FE2021"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="14dp"
        android:layout_height="wrap_content"/>

    <!-- 单价显示 -->
    <TextView
        android:id="@+id/tv_unit_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_2"
        android:background="@android:color/transparent"
        android:paddingHorizontal="6dp"
        android:paddingVertical="2dp"
        android:textColor="@color/color_676773"
        android:textSize="10dp"
        app:layout_constraintStart_toStartOf="@id/tv_price"
        app:layout_constraintTop_toBottomOf="@id/tv_price"
        tools:text=""
        tools:visibility="visible" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_panic_buying"
        android:text="去抢购"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_unit_price"
        app:layout_constraintStart_toStartOf="@id/iv_goods"
        app:layout_constraintEnd_toEndOf="@id/iv_goods"
        android:layout_marginTop="3dp"
        android:layout_width="0dp"
        android:gravity="center"
        app:rv_strokeWidth="1dp"
        app:rv_cornerRadius="3dp"
        app:rv_backgroundColor="@color/color_FFF2F3"
        app:rv_strokeColor="@color/color_FF223B"
        android:textSize="12dp"
        android:textColor="@color/color_FF223B"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_height="24dp"/>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_seckill"
        android:text="去抢购"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_unit_price"
        app:layout_constraintStart_toStartOf="@id/iv_goods"
        app:layout_constraintEnd_toEndOf="@id/iv_goods"
        android:layout_marginTop="3dp"
        android:layout_width="0dp"
        android:gravity="center"
        app:rv_strokeWidth="1dp"
        app:rv_cornerRadius="3dp"
        app:rv_backgroundColor="@color/color_FFF2F3"
        app:rv_strokeColor="@color/color_FF223B"
        android:textSize="12dp"
        android:textColor="@color/color_FF223B"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_height="24dp"/>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_group_buying"
        android:text="立即参团"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_unit_price"
        app:layout_constraintStart_toStartOf="@id/iv_goods"
        app:layout_constraintEnd_toEndOf="@id/iv_goods"
        android:layout_marginTop="3dp"
        android:layout_width="0dp"
        android:gravity="center"
        app:rv_strokeWidth="1dp"
        app:rv_cornerRadius="3dp"
        app:rv_backgroundColor="@color/color_FFf5f0"
        app:rv_strokeColor="@color/color_FF6204"
        android:textSize="12dp"
        android:textColor="@color/color_FF6204"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_height="24dp"/>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_add_to_cart"
        android:text="加入购物车"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_unit_price"
        app:layout_constraintStart_toStartOf="@id/iv_goods"
        app:layout_constraintEnd_toEndOf="@id/iv_goods"
        android:layout_marginTop="3dp"
        android:layout_width="0dp"
        android:gravity="center"
        app:rv_strokeWidth="1dp"
        app:rv_cornerRadius="3dp"
        app:rv_backgroundColor="@color/color_F0FBF5"
        app:rv_strokeColor="@color/color_00B955"
        android:textSize="12dp"
        android:textColor="@color/color_00B955"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_height="24dp"/>

    <!--这个存在的意义是弹窗回调 用里面的请求接口加购-->
    <com.ybmmarket20.view.ProductEditLayoutCommodity
        android:id="@+id/edit_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
    
</androidx.constraintlayout.widget.ConstraintLayout>